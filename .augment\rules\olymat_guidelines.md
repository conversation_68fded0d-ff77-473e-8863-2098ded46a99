---
type: "always_apply"
description: "Example description"
---
# Olymat 自动化框架开发指南 (olymat_guidelines.md)

本文档为在 IDE 中使用的 Augment AI Agent 提供一套 olymat 自动化框架的规则和指南。

---

## 核心规则 (Always - 始终遵循)

这些是最高优先级的规则，你在每一次交互中都必须严格遵守。

1.  **【第一要务】在虚拟环境中执行**: **所有**的 Python 和 `pytest` 命令**必须**在项目的 `venv` 虚拟环境中执行。这意味着你的命令必须明确指向 `venv` 内的可执行文件。
    -   **正确示例**: `venv/Scripts/pytest.exe ...` 或 `venv/Scripts/python.exe ...`
    -   **错误示例**: `pytest ...` 或 `python ...`
2.  **【禁止错误处理】**: 在测试脚本 (`*_test.py`) 中，绝不使用 `try...except` 来捕获并隐藏错误。让代码在出错时直接失败，以便 `pytest` 能够捕获、报告完整的堆栈跟踪信息。
3.  **【禁止日志】**: 绝不使用任何形式的 `logging` 模块或 `print()` 语句进行调试。依赖测试框架的断言和输出来验证行为。
4.  **【禁止休眠】**: 绝不使用 `time.sleep()`。测试应该是确定性的，不应依赖于固定的等待时间。
5.  **【配置与代码分离】**: **严禁**在 Python 脚本中硬编码任何配置值。所有这些值都必须在 `.yaml` 配置文件中定义，并通过 `cfg` 对象在代码中访问。
6.  **【验证所有变更】**: 在完成任何代码修改后，你**必须**运行相关的 `pytest` 命令来验证你的变更没有引入错误，并且功能符合预期。
7.  **【使用 olymat 上下文】**: 在同一个测试流程中的不同测试函数之间传递数据时，**必须**使用 `ctx` 对象。

---

## 场景触发规则 (Auto - 自动触发)

这些规则适用于特定的开发场景。当你识别出用户的意图与以下场景匹配时，应自动遵循相应的流程。

### 场景：当被要求“添加一个新功能”或“创建一个新测试”时 (开发阶段)

1.  **明确位置**: 首先向用户确认，这个新功能应该添加到哪个**工具集 (Toolset)** 的哪个**测试脚本**中。
2.  **定义运行时配置**: 打开 `[toolset]/confs/default.yaml`，为新功能添加并配置运行时参数。**此阶段禁止修改 `template.yaml`**。
3.  **实现功能**: 打开目标 `test_*.py` 文件，创建以 `test_` 开头的函数，该函数必须接受 `cfg` 和 `ctx` 作为参数。
4.  **运行验证**: 使用命令 `venv/Scripts/pytest.exe [脚本路径]::[新函数名]` 单独执行新函数进行验证。

### 场景：当被要求“修改一个现有功能”时

1.  **精准定位**: 找到并确认包含该功能的**工具集目录**和具体的 `test_*.py` 文件。
2.  **全面分析**: 阅读该函数的代码及其在 `confs/default.yaml` 中对应的配置。
3.  **执行修改**: 对 Python 代码或 `default.yaml` 中的配置进行修改。
4.  **运行验证**: 对被修改的函数运行 `pytest` 进行验证。

### 场景：当被要求“发布功能”、“配置UI”或“更新菜单”时 (发布阶段)

1.  **确认功能**: 向用户确认需要发布到 UI 菜单的具体是哪个**工具集 (Toolset)** 下的哪个**功能 (Function)**。
2.  **更新配置模板**: 从 `default.yaml` 文件中，将该功能对应的、已经验证过的配置块，复制并更新到 `[toolset]/confs/template.yaml` 文件中，作为该功能的标准模板。
3.  **更新UI菜单**: 打开 `[toolset]/olymat_ui/conf.yaml` 文件，在 `func_list` 列表中为要发布的功能添加一个新条目。
    - **示例**:
      ```yaml
      - name: "一个清晰的功能名称"
        cmd_param: "test_api_suite/test_user_auth.py"
      ```
4.  **告知完成**: 告知用户配置已更新。UI菜单的最终验证需要通过 olymat-ui 工具进行，这一步由用户完成。

---

## 手动引用规则 (Manual - 可手动@引用)

这部分是速查参考资料，你可以在需要时查阅，用户也可以通过 `@` 手动将其引入对话中。

### 参考：文件与命名约定

| 项目条目              | 命名约定                               | 示例                                  |
| --------------------- | -------------------------------------- | ------------------------------------- |
| **工具集目录**        | **必须**以 `test_` 开头                | `test_smoke_tests`                    |
| **测试脚本文件**      | **必须**以 `test_` 开头                | `test_user_auth.py`                   |
| **测试/功能函数**     | **必须**以 `test_` 开头                | `test_login_successful`               |
| **配置模板文件**      | **必须**命名为 `template.yaml`         | `test_smoke_tests/confs/template.yaml`|
| **UI配置文件**        | **必须**是 `olymat_ui/conf.yaml`       | `test_smoke_tests/olymat_ui/conf.yaml`|

### 参考：常用Shell命令

- **运行单个测试**: `venv/Scripts/pytest.exe [脚本文件路径]::[测试函数名]`
- **运行文件内所有测试**: `venv/Scripts/pytest.exe [脚本文件路径]`
- **运行工具集内所有测试**: `venv/Scripts/pytest.exe [工具集目录路径]/`
- **提交代码**: 
  1. `git status` (检查状态)
  2. `git add .` (暂存变更)
  3. `git commit -m "描述信息"` (提交变更)
