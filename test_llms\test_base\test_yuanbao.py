from allm_helper.llm_begin_helper import chat_helper_factory
import pytest
import os
import tempfile
import time


@pytest.fixture
def yuanbao_client(chat_helper_factory):
    """创建腾讯元宝客户端"""
    yuanbao_agent = chat_helper_factory(
        platform_name="yuanbao",
        user_email="17775747276",  # 腾讯元宝可能支持免登录使用
        conversation_params={
            "model_name": "混元大模型"
        }
    )
    return yuanbao_agent


def test_basic_chat(yuanbao_client):
    """测试基础聊天功能"""
    response = yuanbao_client.chat("1+1等于多少？")
    print(response)
    