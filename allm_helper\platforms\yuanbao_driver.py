from playwright.sync_api import Page
from typing import Optional, List

from allm_helper.platforms.base_platform import BasePlatformDriver
from autils.pw_clipboard_helper import UniversalClipboardInterceptor


class YuanbaoDriver(BasePlatformDriver):
    """
    腾讯元宝平台的具体驱动实现。
    """
    CHAT_URL = "https://yuanbao.tencent.com/chat"

    def __init__(self, page: Page):
        """
        初始化腾讯元宝驱动，只接受 page 参数。
        模型配置将在 new_conversation 时传入。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)

        # 默认配置值
        self.current_model_config = {
            'model_name': "混元大模型",
        }

    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建新会话，并应用模型配置。

        Args:
            model_config: 模型配置字典，包含：
                - model_name: 模型名称
        """
        if model_config:
            self.current_model_config.update(model_config)
        return self

    def use_existing_conversation(self, conversation_title: str):
        """使用已有会话"""
        pass

    def _wait_for_response_complete(self):
        """等待响应完成"""
        self.page.wait_for_selector('.agent-chat__toolbar__item.agent-chat__toolbar__repeat', timeout=60000)

    def _get_reply_by_copy(self):
        """通过复制功能获取回复"""

        copy_btn = self.page.locator('.agent-chat__toolbar__copy__icon').last
        if copy_btn.is_visible():
            with UniversalClipboardInterceptor(self.page) as interceptor:
                copy_btn.click()
                interceptor.wait_for_capture(timeout=5.0)
                return interceptor.text
        assert False, "复制按钮未找到"

    def chat(self, prompt: str, attachments: Optional[List[str]] = None,
             response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。

        :param prompt: 用户输入的提示。
        :param attachments: 要上传的附件的本地文件路径列表。
        :param response_format: 响应格式，可选 "text"。
        :return: AI的回复文本。
        """

        input_element = self.page.locator('.ql-editor').last
        # 点击输入框激活并输入文本
        input_element.click()
        input_element.fill(prompt)

        # 发送消息
        self.page.locator('#yuanbao-send-btn').click()
        # 等待回复完成并获取回复
        self._wait_for_response_complete()
        return self._get_reply_by_copy()

    def is_ready(self) -> bool:
        """检查平台是否准备好接收新消息"""
        pass

    def save_chat(self, chat_name: str):
        """保存当前会话并命名"""
        pass

    def del_chat(self, chat_name: str):
        """删除会话"""
        pass
