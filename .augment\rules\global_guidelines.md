---
type: "always_apply"
---

# Agent 通用开发指南 (global_guidelines.md)

本文档为在 IDE 中使用的 Augment AI Agent 提供一套通用的规则和指南。请严格遵守这些指南以确保项目代码的统一、规范和高质量。

---

## Agent 交互准则

**【禁止废话】**: 你的回答**只应包含代码**。除非我特别要求，否则不要添加任何解释、注释或总结。

---

## 核心规则 (Always - 始终遵循)

这些是最高优先级的规则，你在每一次交互中都必须严格遵守。

1.  **【遵循约定】**: 你的首要任务是理解并严格遵守项目中已有的结构、命名约定和编码风格。绝不引入任何破坏现有约定的变更。
2.  **【单一实现】**: 对于任何功能，只提供一种最直接、最简单的实现路径。禁止提供备用方案或复杂的选择逻辑。
3.  **【最少代码】**: 只编写为实现我要求所必需的最少代码。
4.  **【先分析，后行动】**: 在创建或修改任何文件之前，你必须先读取相关的现有文件来理解上下文。